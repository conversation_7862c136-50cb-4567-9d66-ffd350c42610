// Packages
const { <PERSON>ronJob } = require('cron');

// Services
const uploadCertificateService = require('../../services/upload-certificate.service');
const memberService = require('../../services/member.service');

// Utils
const commonFunctionsUtils = require('../../utils/common-function.utils');
const mailerUtils = require('../../utils/mailer.utils');
const constantsUtils = require('../../utils/constants.utils');

/**
 * Job to send certificate expiry reminders to users
 */
const certificateReminderJob = new CronJob('*/10 * * * *', async () => {
  console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_STARTED, new Date().toLocaleString());

  try {
    const certificates = await uploadCertificateService.getUserUploadCertificate({
      isActive: true,
      status: 'approved',
      internal: false,
      deletedAt: null,
    });

    const currentDate = commonFunctionsUtils.getCurrentTimeInTimezone(
      global.constant.DEFAULT_TIMEZONE
    );
    const template = process.env.SENDGRID_CERTIFICATE_EXPIRY_REMAINDER;
    const reminderWindows = global.constant.CERTIFICATE_EXPIRY;

    for (const cert of certificates) {
      const { user, endDate, name } = cert;
      if (!endDate) continue;

      if (!user?.isActive || !user.email) continue;

      const memberProjects = await memberService.getAllMember({
        user: user._id,
        deletedAt: null,
      });

      const isAssigned = memberProjects.some(member => member.project?.isActive);
      if (!isAssigned) continue;

      const expiryDate = commonFunctionsUtils.getCurrentTimeInTimezone(
        global.constant.DEFAULT_TIMEZONE,
        endDate
      );
      const daysRemaining = Math.floor((expiryDate - currentDate) / global.constant.DAY_CONVERTOR);

      if (isNaN(daysRemaining)) continue;

      const months = Math.floor(Math.abs(daysRemaining) / global.constant.DAYS_OF_MONTH);
      const days = Math.abs(daysRemaining) % global.constant.DAYS_OF_MONTH;

      const formattedDuration =
        [months > 0 ? `${months} month(s)` : '', days > 0 ? `${days} day(s)` : '']
          .filter(Boolean)
          .join(' and ') || '0 day(s)';

      // Match the reminder window
      const window = reminderWindows.find(window => window.days === daysRemaining);

      if (!window) continue;
      console.log('testing cron', user.email);

      await mailerUtils.sendMailer(user.email.toString(), template, {
        expiryDate: endDate,
        certificateName: name,
        logo:
          months > 0 && days > 0
            ? global.constant.CERTIFICATE_ALERT_LOGO
            : global.constant.CERTIFICATE_EXPIRED_LOGO,
        daysAfterExpiry: formattedDuration,
        userFullName: `${user.callingName} ${user.lastName}`,
        email: user.email,
        supportTeamEmail: process.env.SUPPORT_TEAM_EMAIL,
        currentYear: new Date().getFullYear(),
        bestRegards: process.env.BEST_REGARDS,
        organizationName: 'Reynard',
      });
    }

    console.log(constantsUtils.CERTIFICATE_REMAINDER_CRON_COMPLETED);
  } catch (error) {
    console.error(constantsUtils.ERROR_CERTIFICATE_REMAINDER_CRON, error);
  }
});

module.exports = certificateReminderJob;

// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');

// constants
const constants = require('../utils/constants.utils');

// Services
const shiftServices = require('../services/shift.service');
const teamMemberService = require('../services/team-member.service');
const memberService = require('../services/member.service');
const shiftActvityService = require('../services/shift-activity.service');
const projectService = require('../services/project.service');
const safetyCardService = require('../services/safety-card.service');
const pdfTemplateService = require('../services/pdf-template.service');
const notInListService = require('../services/not-in-list.service');
const functionService = require('../services/function.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const modifyDefaultDataUtils = require('../utils/modify-default-data.utils');
const exportExcelUtils = require('../utils/export-excel.util');
const commonFunctionsUtils = require('../utils/common-function.utils');
const HTTP_STATUS = require('../utils/status-codes');

const mongoose = require('mongoose');
const dateFormat = require('dateformat');

/**
 * Create New Shift
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createShift = async (req, res) => {
  try {
    let { member, project, startDate, endDate, duration } = req.body;
    let reqData = req.body;
    const projectName = await projectService.getProjectById(reqData.project, req.userData.account);

    // If default project is selected
    if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.defaultProject = await safetyCardService.getDefaultProject(req);
      reqData.isDefault = true;
      reqData.defaultIdentifier = 'default';
    } else {
      reqData.defaultProject = null;
      reqData.isDefault = false;
    }

    // team
    if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.team = await modifyDefaultDataUtils.getDefaultTeam(req);
      reqData.isDefault = true;
    }
    // team

    // member
    if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      member = await modifyDefaultDataUtils.getDefaultMember(
        member,
        projectName._id,
        req.userData.account
      );
    }

    // member
    // End of if default project is selected
    const newReq = {
      ...reqData,
      account: req.userData.account,
      createdBy: req.userData._id,
      updatedBy: req.userData._id,
      project: project,
      startDate,
      endDate,
      duration,
      status: Object.hasOwn(req.body, 'status') ? req.body.status : 'open',
    };

    const createdShift = await shiftServices.createShift(newReq);
    const memberData = await memberService.getMemberByIds(member);

    for (const member of memberData) {
      const memberReq = {
        member: member._id,
        function: member.function,
        shift: createdShift._id,
        createdBy: req.userData._id,
        updatedBy: req.userData._id,
      };
      await teamMemberService.createTeamMember(memberReq);
    }

    return res.status(200).json(successResponse(constantUtils.CREATE_SHIFT, createdShift));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateShift = async (req, res) => {
  try {
    let { member } = req.body;

    let id = req.params.id;

    const exist = await shiftServices.getShiftById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }

    const projectName = await projectService.getProjectById(
      exist.project._id,
      req.userData.account
    );
    await this.handleDefaultProject(req, projectName);
    await this.handleDefaultTeam(req, projectName);
    await this.handleDefaultMember(req, member, projectName);
    await this.handleMembers(req, id, member);
    //

    //
    // member
    // End of if default project is selected
    req.body.updatedBy = req.userData._id;
    const updatedShift = await shiftServices.updateShift(id, req.body);

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(successResponse(constants.UPDATE_SHIFT, updatedShift));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Shifts
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllShifts = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';

    let queryProject = typeof req.query.project === 'undefined' ? 'all' : req.query.project;
    let filterData = req.query;
    if (filterData.date !== undefined) {
      delete filterData.page;
      delete filterData.perPage;
    }
    filterData.created = filterData.date;
    delete filterData.date;
    filterData = await commonUtils.filterParamsModify(filterData);
    filterData = await shiftServices.getStartDateFilter(filterData);

    // check login user assign projects & filter
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject === 'all'
    ) {
      let searchData = {
        user: req.userData._id,
        account: req.userData.account,
        deletedAt: null,
      };

      let projectList = await memberService.fetchAssignProjects(searchData);

      if (projectList.length > 0) {
        Object.keys(projectList).forEach(key => {
          projectList[+key] = commonUtils.toObjectId(projectList[+key]);
        });
        filterData.project = { $in: projectList };
      }
    } else if (mongoose.Types.ObjectId.isValid(queryProject)) {
      filterData.project = commonUtils.toObjectId(queryProject);
    } else {
      delete filterData.project;
    }
    /*-- End --*/

    let { projectStatus } = req.query;

    // Add project filter if project status is provided
    if (projectStatus && queryProject === 'all') {
      filterData = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filterData
      );
    } else {
      if (req.userData.role.isAssignAllProjects && queryProject === 'all') {
        const projectList = await projectService.getAllProjects({
          account: req.userData.account,
          deletedAt: null,
        });

        if (projectList && projectList.length > 0) {
          filterData = {
            ...filterData,
            project: {
              $in: projectList.map(project => project._id),
            },
          };
        }
      }
    }

    let shiftList = await shiftServices.getAllShiftsOptimized(
      filterData,
      req.userData.account,
      page,
      perPage
    );
    shiftList.currentPage = Number(page);
    filterData.account = req.userData.account;
    filterData.deletedAt = null;
    delete filterData.projectStatus;
    shiftList = await commonUtils.getCountFromQuery('shift', filterData, shiftList);
    return res.status(200).json(successResponse(constantUtils.ALL_SHIFT_LIST, shiftList));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteShift = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await shiftServices.getShiftById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }

    await shiftServices.deleteShift(id, req.deletedAt);
    const teamMembers = await teamMemberService.getTeamMemberByShiftId(id);

    teamMembers.forEach(async member => {
      await teamMemberService.deleteTeamMember(member._id, req.deletedAt);
    });

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(responseUtils.successResponse(constantUtils.DELETE_SHIFT));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Filter shift
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.filterShift = async (req, res) => {
  try {
    // Filter tasks based on project, date, and status
    let shift = await shiftServices.getAllShifts(req.userData.account);
    let filteredShift = await shiftServices.filterShift(req, shift);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_SHIFT_LIST, filteredShift));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Shift Activities By Shift
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getShiftActvityData = async (req, res) => {
  try {
    let filter = { shift: req.params.id };
    let getData = await shiftActvityService.getShiftActivityDataByFilter(filter);

    if (!getData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SHIFT_ACTVITY_NOT_EXIST));
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.SHIFT_ACTVITY_LIST, getData));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get All Shift Activities
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getAllShiftActivities = async (req, res) => {
  try {
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let getData = await shiftActvityService.getAllRecord({}, sort);

    return res.status(200).json(successResponse(constantUtils.SHIFT_ACTVITY_LIST, getData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get single shift by filter
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getSingleShiftById = async (req, res) => {
  try {
    let { id } = req.params;

    if (!commonUtils.isValidId(id)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SHIFT_ID));
    }

    let filterData = { id: id };

    const shift = await shiftServices.getAllShifts(filterData, req.userData.account, '', '');

    if (shift.shiftCount === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }

    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      if (projectList.length === 0) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_ACCESS));
      }
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.SINGLE_SHIFT, shift.shiftData[0]));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get All Shifts Count
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getAllShiftsCount = async (req, res) => {
  try {
    let project =
      req.query.project !== undefined ? commonUtils.toObjectId(req.query.project) : null;

    let filter = {
      account: req.userData.account,
      ...(project !== null && { project }),
      deletedAt: null,
    };

    let response = await shiftServices.getAllShiftsCount(filter);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.SINGLE_SHIFT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.handleMembers = async (req, id, member) => {
  if (Object.hasOwn(req.body, 'member')) {
    const teamMembers = await teamMemberService.getTeamMemberByShiftId(id);

    teamMembers.forEach(async member => {
      await teamMemberService.deleteTeamMember(member._id, req.deletedAt);
    });

    const memberData = await memberService.getMemberByIds(member);

    for (const memberD of memberData) {
      const memberReq = {
        member: memberD._id,
        function: memberD.function,
        shift: id,
        createdBy: req.userData._id,
        updatedBy: req.userData._id,
      };
      await teamMemberService.createTeamMember(memberReq);
    }
  }
};
exports.handleDefaultProject = async (req, projectName) => {
  if (projectName && projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    if ('project' in req.body) {
      req.body.defaultProject = await safetyCardService.getDefaultProject(req);
      req.body.isDefault = true;
      req.body.defaultIdentifier = 'default';
    }
  } else {
    req.body.defaultProject = null;
    req.body.isDefault = false;
  }
};

exports.handleDefaultTeam = async (req, projectName) => {
  if (projectName && projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    if ('team' in req.body) {
      req.body.team = await modifyDefaultDataUtils.getDefaultTeam(req);
      req.body.isDefault = true;
    }
  }
};

exports.handleDefaultMember = async (req, member, projectName) => {
  if (projectName && projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    if ('member' in req.body) {
      req.body.member = await modifyDefaultDataUtils.getDefaultMember(
        member,
        projectName._id,
        req.userData.account
      );
    }
  }
};

/**
 * Handle Not In List
 *
 * @param {*} req
 * @param {*} notInList
 */
exports.handleNotInList = async req => {
  let id = req.params.id;
  if ('notInList' in req.body && req.body.notInList.length > 0) {
    const notInList = req.body.notInList;

    for (const element of notInList) {
      const id = commonUtils.toObjectId(element._id);

      const rest = { ...element };
      delete rest._id;

      await notInListService.updateNotInList({ _id: id }, rest);
    }

    const getNotInListData = await notInListService.getNotInListMembers({
      shift: id,
      deletedAt: null,
    });

    if (notInList.length < getNotInListData.length) {
      const result = getNotInListData.filter(
        item => !notInList.map(n => n._id).includes(item._id.toString())
      );

      for (const element of result) {
        await notInListService.updateNotInList({ _id: element._id }, { deletedAt: new Date() });
      }
    }
  } else if ('notInList' in req.body && req.body.notInList.length === 0) {
    await notInListService.updateNotInListByFilter({ shift: id }, { deletedAt: new Date() });
  }
};

exports.handleMembersInOtherProject = async (req, id, member, project) => {
  if (Object.hasOwn(req.body, 'member')) {
    const teamMembers = await teamMemberService.getTeamMemberByShiftId(id);

    teamMembers.forEach(async member => {
      await teamMemberService.deleteTeamMember(member._id, req.deletedAt);
    });
    const functionName = await functionService.getDefaultFunction();
    if (project.standByTypes === global.constant.OTHER_PROJECT) {
      // eslint-disable-next-line no-undef
      let createdMembers = await Promise.all(
        member.map(element =>
          memberService.createMember({
            project: project._id,
            function: functionName._id, // default function
            user: element,
            createdBy: req.userData._id,
            updatedBy: req.userData._id,
            account: req.userData.account,
          })
        )
      );

      member = createdMembers.map(m => m._id);
    }

    const memberData = await memberService.getMemberByIds(member);

    for (const memberD of memberData) {
      const memberReq = {
        member: memberD._id,
        function: memberD.function,
        shift: id,
        createdBy: req.userData._id,
        updatedBy: req.userData._id,
      };
      await teamMemberService.createTeamMember(memberReq);
    }
  }
};

/**
 * Get Shift PDF Details
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getShiftPDFDetails = async (req, res) => {
  try {
    const { shiftId } = req.params;

    const filter = {
      _id: commonUtils.toObjectId(shiftId),
      account: req.userData.account,
      deletedAt: null,
    };

    let [response] = await shiftServices.getShiftPDFDetails(filter);
    response.teamMembers = await teamMemberService.getTeamMemberByShiftId(shiftId);

    const notInListMembers = await notInListService.getNotInListMembers({
      shift: shiftId,
      account: req.userData.account,
      deletedAt: null,
    });

    let result;

    if (notInListMembers && notInListMembers.length > 0) {
      result = notInListMembers.map(element => ({
        _id: element._id,
        member: {
          _id: element._id,
          project: element.project,
          function: element.functionName,
          user: {
            _id: element._id,
            firstName: '',
            lastName: '',
            callingName: element.memberName,
          },
          account: req.userData.account,
        },
        function: {
          _id: element._id,
          functionName: element.functionName,
        },
        shift: commonUtils.toObjectId(shiftId),
        isWorking: true,
        status: true,
      }));
    }

    response.teamMembers =
      notInListMembers.length > 0 ? [...response.teamMembers, ...result] : response.teamMembers;

    if (response.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_ORDER_DATA));
    }

    return await pdfTemplateService.exportShiftPDF(response, res);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Shift excel
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getShiftExcel = async (req, res) => {
  try {
    let queryProject = req.query.project ? req.query.project : 'all';
    let filterData = req.query;
    const nowDate = dateFormat(new Date(), global.constant.DATE_FORMAT_FOR_EXPORT);
    let fileName = `Shift-${nowDate}`;
    filterData.created = filterData.date;
    delete filterData.date;
    filterData = await commonUtils.filterParamsModify(filterData);
    filterData = await shiftServices.getCreatedDateFilter(filterData);
    filterData.deletedAt = null;
    // check login user assign projects & filter
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject === 'all'
    ) {
      let searchData = {
        user: req.userData._id,
        account: req.userData.account,
        deletedAt: null,
      };

      let projectList = await memberService.fetchAssignProjects(searchData);

      if (projectList.length > 0) {
        Object.keys(projectList).forEach(key => {
          projectList[+key] = commonUtils.toObjectId(projectList[+key]);
        });
        filterData.project = { $in: projectList };
      }
    } else if (mongoose.Types.ObjectId.isValid(queryProject)) {
      filterData.project = commonUtils.toObjectId(queryProject);
    } else {
      filterData.project = '';
    }
    if (req.userData.role.isAssignAllProjects && queryProject === 'all') {
      const projectList = await projectService.getAllProjects({
        account: req.userData.account,
        deletedAt: null,
      });

      if (projectList && projectList.length > 0) {
        filterData = {
          ...filterData,
          project: {
            $in: projectList.map(project => project._id),
          },
        };
      }
    }
    /*-- End --*/

    let shiftList = await shiftServices.getAllShiftsForExcel(filterData, req.userData.account);
    if (shiftList.shiftData.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }
    let shiftDetails = [];
    for (let shift of shiftList.shiftData) {
      let shiftActivities = await shiftActvityService.getShiftActivityDetails(shift._id);
      if (shiftActivities && shiftActivities.length > 0) {
        shiftActivities = await commonFunctionsUtils.calculateDurationsHours(
          shiftActivities,
          shift.startDate
        );
        for (let activity of shiftActivities) {
          const newShift = JSON.parse(JSON.stringify(shift));
          newShift.shiftActivities = activity;
          shiftDetails.push(newShift);
        }
      }
    }
    const tableData = await commonFunctionsUtils.getTableData(
      shiftDetails,
      commonUtils.shiftTableHeaders,
      commonUtils.shiftRowMapper
    );

    await exportExcelUtils.exportExcel(res, fileName, tableData.columns, tableData.rows, 'shifts');
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get All Shifts User wise
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllShiftsUserWise = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';

    let queryProject = typeof req.query.project === 'undefined' ? 'all' : req.query.project;
    let filterData = req.query;
    if (filterData.date !== undefined) {
      delete filterData.page;
      delete filterData.perPage;
    }
    filterData.created = filterData.date;
    delete filterData.date;
    filterData = await commonUtils.filterParamsModify(filterData);
    filterData = await shiftServices.getStartDateFilter(filterData);

    // check login user assign projects & filter
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject === 'all'
    ) {
      let searchData = {
        user: req.userData._id,
        account: req.userData.account,
        deletedAt: null,
      };

      let projectList = await memberService.fetchAssignProjects(searchData);

      if (projectList.length > 0) {
        Object.keys(projectList).forEach(key => {
          projectList[+key] = commonUtils.toObjectId(projectList[+key]);
        });
        filterData.project = { $in: projectList };
      }
    } else if (mongoose.Types.ObjectId.isValid(queryProject)) {
      filterData.project = commonUtils.toObjectId(queryProject);
    } else {
      delete filterData.project;
    }
    /*-- End --*/

    let { projectStatus } = req.query;

    // Add project filter if project status is provided
    if (projectStatus && queryProject === 'all') {
      filterData = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filterData
      );
    } else {
      if (req.userData.role.isAssignAllProjects && queryProject === 'all') {
        const projectList = await projectService.getAllProjects({
          account: req.userData.account,
          deletedAt: null,
        });

        if (projectList && projectList.length > 0) {
          filterData = {
            ...filterData,
            project: {
              $in: projectList.map(project => project._id),
            },
          };
        }
      }
    }

    let shiftList = await shiftServices.getAllShifts(
      filterData,
      req.userData.account,
      page,
      perPage,
      commonUtils.toObjectId(req.userData.id)
    );
    shiftList.currentPage = Number(page);
    filterData.account = req.userData.account;
    filterData.deletedAt = null;
    filterData.createdBy = req.userData.id;
    shiftList = await commonUtils.getCountFromQuery('shift', filterData, shiftList);
    return res.status(200).json(successResponse(constantUtils.ALL_SHIFT_LIST, shiftList));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Create New Shift
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createShiftV2 = async (req, res) => {
  try {
    let { member, project, startDate, endDate, duration, notInList = [] } = req.body;
    let reqData = req.body;

    const projectName = await projectService.getProjectById(reqData.project, req.userData.account);
    let response;

    // If default project is selected
    if (projectName.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.defaultProject = await safetyCardService.getDefaultProject(req);
      reqData.isDefault = true;
      reqData.defaultIdentifier = 'default';
      reqData.team = await modifyDefaultDataUtils.getDefaultTeam(req);
      member = await modifyDefaultDataUtils.getDefaultMember(
        member,
        projectName._id,
        req.userData.account
      );
    } else {
      reqData.defaultProject = null;
      reqData.isDefault = false;
    }

    // Prepare new request
    const newReq = {
      ...reqData,
      account: req.userData.account,
      createdBy: req.userData._id,
      updatedBy: req.userData._id,
      project,
      startDate,
      endDate,
      duration,
      status: Object.hasOwn(req.body, 'status') ? req.body.status : 'open',
    };

    // Create shift
    const createdShift = await shiftServices.createShift(newReq);

    // Important: set response immediately
    response = createdShift.toObject();
    response.notInList = [];

    // Handle members
    const memberData = await memberService.getMemberByIds(member);
    for (const memberItem of memberData) {
      const memberReq = {
        member: memberItem._id,
        function: memberItem.function,
        shift: createdShift._id,
        createdBy: req.userData._id,
        updatedBy: req.userData._id,
      };
      await teamMemberService.createTeamMember(memberReq);
    }

    /** ------------------------------------------- Handle Not in List ------------------------------------------- */
    if (notInList && notInList.length) {
      const projectObjectId = commonUtils.toObjectId(project);

      for (const element of notInList) {
        const result = await notInListService.addMemberAndFunctionToNotInList({
          ...element,
          shift: createdShift._id,
          project: projectObjectId,
          account: req.userData.account,
          createdBy: req.userData._id,
          updatedBy: req.userData._id,
        });

        if (Array.isArray(result)) {
          result.forEach(item => {
            const { _id, memberName, functionName } = item;
            response.notInList.push({ _id, memberName, functionName, notInList: true });
          });
        } else if (result) {
          const { _id, memberName, functionName } = result;
          response.notInList.push({ _id, memberName, functionName, notInList: true });
        }
      }
    }

    const memberIds = memberData.map(member => member._id);
    const memberList = await teamMemberService.getTeamMemberByProjectIds({
      shift: createdShift._id,
      member: { $in: memberIds },
    });

    for (const element of memberList) {
      response.notInList.push({
        _id:
          projectName.standByTypes === global.constant.OTHER_PROJECT
            ? element?.member?.user?._id
            : element?.member?._id,
        memberName: `${element?.member?.user?.callingName || element?.member?.user?.firstName} ${
          element?.member?.user?.lastName
        }`,
        functionName: element?.function?.functionName,
        notInList: false,
      });
    }
    /** ------------------------------------------- End of Not in List ------------------------------------------- */

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.CREATE_SHIFT, response));
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateShiftV2 = async (req, res) => {
  try {
    let { member } = req.body;

    let id = req.params.id;

    const exist = await shiftServices.getShiftById(id);

    if (!exist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }

    const projectName = await projectService.getProjectById(
      exist.project._id,
      req.userData.account
    );

    await this.handleDefaultProject(req, projectName);
    await this.handleDefaultTeam(req, projectName);
    await this.handleDefaultMember(req, member, projectName);
    await this.handleMembersInOtherProject(req, id, member, projectName);
    await this.handleNotInList(req);

    // member
    // End of if default project is selected
    req.body.updatedBy = req.userData._id;
    const updatedShift = await shiftServices.updateShift(id, req.body);

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(HTTP_STATUS.OK).json(successResponse(constants.UPDATE_SHIFT, updatedShift));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunctionsUtils.updateSyncApiManage({
    syncApis: ['shifts'],
    account,
  });
};

/**
 * Get All Shifts(V1 code)
 *
 * @param {*} req
 * @param {*} res
 */
exports.getV1AllShifts = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let queryProject = typeof req.query.project === 'undefined' ? 'all' : req.query.project;
    let filterData = req.query;
    if (filterData.date !== undefined) {
      delete filterData.page;
      delete filterData.perPage;
    }
    filterData.created = filterData.date;
    delete filterData.date;
    filterData = await commonUtils.filterParamsModify(filterData);
    filterData = await shiftServices.getStartDateFilter(filterData);
    // check login user assign projects & filter
    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject === 'all'
    ) {
      let searchData = {
        user: req.userData._id,
        account: req.userData.account,
        deletedAt: null,
      };
      let projectList = await memberService.fetchAssignProjects(searchData);
      if (projectList.length > 0) {
        Object.keys(projectList).forEach(key => {
          projectList[+key] = commonUtils.toObjectId(projectList[+key]);
        });
        filterData.project = { $in: projectList };
      }
    } else if (mongoose.Types.ObjectId.isValid(queryProject)) {
      filterData.project = commonUtils.toObjectId(queryProject);
    } else {
      delete filterData.project;
    }
    /*-- End --*/
    let { projectStatus } = req.query;
    // Add project filter if project status is provided
    if (projectStatus && queryProject === 'all') {
      filterData = await commonUtils.filterProjectStatus(
        projectStatus,
        req.userData.account,
        filterData
      );
    } else {
      if (req.userData.role.isAssignAllProjects && queryProject === 'all') {
        const projectList = await projectService.getAllProjects({
          account: req.userData.account,
          deletedAt: null,
        });
        if (projectList && projectList.length > 0) {
          filterData = {
            ...filterData,
            project: {
              $in: projectList.map(project => project._id),
            },
          };
        }
      }
    }

    let shiftList = await shiftServices.getAllShifts(
      filterData,
      req.userData.account,
      page,
      perPage
    );
    shiftList.currentPage = Number(page);
    filterData.account = req.userData.account;
    filterData.deletedAt = null;
    delete filterData.projectStatus;
    shiftList = await commonUtils.getCountFromQuery('shift', filterData, shiftList);
    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.ALL_SHIFT_LIST, shiftList));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
